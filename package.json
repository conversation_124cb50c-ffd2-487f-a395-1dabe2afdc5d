{"name": "railway-luggage-tracking", "version": "1.0.0", "description": "Railway Luggage Tracking & Security Web App - MERN Stack", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cd backend && npm run dev", "client": "cd frontend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install", "build": "cd frontend && npm run build", "start": "cd backend && npm start"}, "keywords": ["railway", "luggage", "tracking", "security", "mern", "qr-code"], "author": "Railway Luggage Tracking Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}