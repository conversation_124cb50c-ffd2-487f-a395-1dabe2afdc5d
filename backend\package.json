{"name": "railway-luggage-backend", "version": "1.0.0", "description": "Backend API for Railway Luggage Tracking System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "qrcode": "^1.5.3", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "firebase-admin": "^11.11.1", "multer": "^1.4.5-lts.1", "crypto": "^1.0.1", "moment": "^2.29.4", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["express", "mongodb", "jwt", "qr-code", "notifications"], "author": "Railway Luggage Tracking Team", "license": "MIT"}