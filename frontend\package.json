{"name": "railway-luggage-frontend", "version": "1.0.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "axios": "^1.6.2", "qrcode.react": "^3.1.0", "react-qr-scanner": "^1.0.0-alpha.11", "react-webcam": "^7.1.1", "jsqr": "^1.4.0", "react-toastify": "^9.1.3", "react-hook-form": "^7.48.2", "socket.io-client": "^4.7.4", "moment": "^2.29.4", "lucide-react": "^0.294.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "proxy": "http://localhost:5000"}